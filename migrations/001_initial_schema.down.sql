BEGIN;

-- Drop triggers
DROP TRIGGER IF EXISTS update_userprofile_updated_at ON userprofile;

-- Drop function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_orbstar_value;
DROP INDEX IF EXISTS idx_orbstar_receiver_id;
DROP INDEX IF EXISTS idx_orbstar_giver_id;
DROP INDEX IF EXISTS idx_orbstar_created_at;
DROP INDEX IF EXISTS idx_userprofile_show;
DROP INDEX IF EXISTS idx_userprofile_email;

-- Drop tables
DROP TABLE IF EXISTS orbstar;
DROP TABLE IF EXISTS userprofile;

-- Drop enum type
DROP TYPE IF EXISTS "Values";

COMMIT;