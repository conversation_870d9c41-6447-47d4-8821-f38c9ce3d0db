BEGIN;

-- Create Values enum type
CREATE TYPE "Values" AS ENUM (
    'Self Development',
    'Innovation',
    'Initiative',
    'Authenticity',
    'Impact',
    'Reliability'
);

-- Create userprofile table
CREATE TABLE userprofile (
    id SERIAL PRIMARY KEY,
    email VARCHAR(64) UNIQUE NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(24) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(24) NOT NULL,
    show BOOLEAN DEFAULT FALSE NOT NULL,
    admin BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create orbstar table
CREATE TABLE orbstar (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP DEFAULT NOW(),
    value "Values" NOT NULL,
    description TEXT NOT NULL,
    giver_id INTEGER REFERENCES userprofile(id) ON DELETE CASCADE,
    receiver_id INTEGER REFERENCES userprofile(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_userprofile_email ON userprofile(email);
CREATE INDEX idx_userprofile_show ON userprofile(show);
CREATE INDEX idx_orbstar_created_at ON orbstar(created_at);
CREATE INDEX idx_orbstar_giver_id ON orbstar(giver_id);
CREATE INDEX idx_orbstar_receiver_id ON orbstar(receiver_id);
CREATE INDEX idx_orbstar_value ON orbstar(value);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_userprofile_updated_at 
    BEFORE UPDATE ON userprofile 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

COMMIT;